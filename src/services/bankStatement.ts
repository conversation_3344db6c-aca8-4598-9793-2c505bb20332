import { ApiListResponse, ApiResponse } from "../types/api";
import {
  BankStatementItem,
  BankStatementFormData,
  BankStatementParams,
} from "../types/bankStatement";
import api from "./api";

// 获取银行流水列表
export const getBankStatements = async (
  params: BankStatementParams
): Promise<ApiListResponse<BankStatementItem[]>> => {
  const response = await api.get("/bank-statements", { params });
  return response.data;
};

// 更新银行流水
export const updateBankStatement = async (
  id: number,
  data: BankStatementFormData
): Promise<ApiResponse<any>> => {
  // 转换数据格式以匹配API要求
  const formattedData = {
    ...data,
    amount: data.amount?.toString() || "",
    statement_date: data.statement_date ?
      data.statement_date.toISOString().split('T')[0] : "",
    confirming_amount: data.confirming_amount?.toString() || "",
    confirmed_amount: data.confirmed_amount?.toString() || "",
  };

  const response = await api.put(`/bank-statements/${id}`, formattedData);
  return response.data;
};

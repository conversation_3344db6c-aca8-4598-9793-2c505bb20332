<script setup lang="ts">
import { useToast } from "primevue/usetoast";
import { onMounted, ref } from "vue";
import { usePermission } from "../../composables/usePermission";
import {
  getBankStatements,
  updateBankStatement,
} from "../../services/bankStatement";
import type {
  BankStatementItem,
  BankStatementFormData,
  BankStatementParams,
} from "../../types/bankStatement";
import { formatDateTime, formatCurrency } from "../../utils/common";
import { bankStatementStateOptions } from "../../utils/const";
import { optionLoaders } from "../../utils/options";

const bankStatements = ref<BankStatementItem[]>([]);
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选参数
const filterState = ref<number | null>(null);
const filterStatementNo = ref("");
const filterPaymentName = ref("");

// 加载银行流水列表
const loadBankStatements = async () => {
  loading.value = true;
  try {
    const params: BankStatementParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (filterState.value !== null) {
      params.receipt_status = filterState.value;
    }
    if (filterStatementNo.value.trim()) {
      params.statement_no = filterStatementNo.value.trim();
    }
    if (filterPaymentName.value.trim()) {
      params.payment_name = filterPaymentName.value.trim();
    }

    const response = await getBankStatements(params);
    bankStatements.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载银行流水失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadBankStatements();
};

// 搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadBankStatements();
};

// 重置筛选
const resetFilters = () => {
  filterState.value = null;
  filterStatementNo.value = "";
  filterPaymentName.value = "";
  lazyParams.value.page = 1;
  loadBankStatements();
};

onMounted(async () => {
  await initializeUserInfo();
  loadCurrencyOptions();
  loadBankStatements();
});

// 表单相关
const bankStatementDrawerVisible = ref(false);
const editingBankStatement = ref<BankStatementItem | null>(null);
const isSubmitting = ref(false);
const submitted = ref(false);
const fieldErrors = ref<Record<string, string>>({});

const bankStatementForm = ref<BankStatementFormData>({
  amount: null,
  currency_type: "",
  statement_no: "",
  statement_date: null,
  payment_name: "",
  payment_bank_no: "",
  payment_bank_name: "",
  receive_bank_name: "",
  receive_bank_no: "",
  confirming_amount: null,
  confirmed_amount: null,
  customer_num: "",
  customer_name: "",
  description: "",
});

// 货币类型选项
const currencyOptions = ref<{ label: string; value: string }[]>([]);
const loadCurrencyOptions = () => optionLoaders.currencyType(currencyOptions);

// 清除字段错误
const clearFieldErrors = () => {
  fieldErrors.value = {};
  submitted.value = false;
};

// 编辑银行流水
const editBankStatement = (bankStatement: BankStatementItem) => {
  editingBankStatement.value = bankStatement;
  bankStatementForm.value = {
    amount: parseFloat(bankStatement.amount) || null,
    currency_type: bankStatement.currency_type,
    statement_no: bankStatement.statement_no,
    statement_date: bankStatement.statement_date
      ? new Date(bankStatement.statement_date)
      : null,
    payment_name: bankStatement.payment_name,
    payment_bank_no: bankStatement.payment_bank_no,
    payment_bank_name: bankStatement.payment_bank_name,
    receive_bank_name: bankStatement.receive_bank_name,
    receive_bank_no: bankStatement.receive_bank_no,
    confirming_amount: parseFloat(bankStatement.confirming_amount) || null,
    confirmed_amount: parseFloat(bankStatement.confirmed_amount) || null,
    customer_num: bankStatement.customer_num || "",
    customer_name: bankStatement.customer_name || "",
    description: bankStatement.description,
  };
  clearFieldErrors();
  bankStatementDrawerVisible.value = true;
};

// 验证必填字段
const validateForm = () => {
  const errors: Record<string, string> = {};

  if (!bankStatementForm.value.amount) {
    errors.amount = "金额不能为空";
  }
  if (!bankStatementForm.value.currency_type.trim()) {
    errors.currency_type = "货币类型不能为空";
  }
  if (!bankStatementForm.value.statement_no.trim()) {
    errors.statement_no = "流水号不能为空";
  }
  if (!bankStatementForm.value.statement_date) {
    errors.statement_date = "流水日期不能为空";
  }
  if (!bankStatementForm.value.payment_name.trim()) {
    errors.payment_name = "付款方名称不能为空";
  }

  fieldErrors.value = errors;
  return Object.keys(errors).length === 0;
};

// 保存银行流水
const saveBankStatement = async () => {
  submitted.value = true;

  if (!validateForm()) {
    toast.add({
      severity: "error",
      summary: "验证失败",
      detail: "请填写必填字段",
      life: 3000,
    });
    return;
  }

  isSubmitting.value = true;
  try {
    await updateBankStatement(
      editingBankStatement.value!.id,
      bankStatementForm.value
    );
    toast.add({
      severity: "success",
      summary: "成功",
      detail: "银行流水更新成功",
      life: 3000,
    });
    bankStatementDrawerVisible.value = false;
    clearFieldErrors();
    loadBankStatements();
  } catch (error: any) {
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "保存银行流水失败",
        life: 3000,
      });
    }
  } finally {
    isSubmitting.value = false;
  }
};

// 关闭抽屉
const closeDrawer = () => {
  bankStatementDrawerVisible.value = false;
  clearFieldErrors();
};
</script>

<template>
  <div class="bank-flow-container">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">
          <i class="pi pi-credit-card"></i>
          银行流水管理
        </h2>
      </div>

      <!-- 筛选区域 -->
      <Toolbar class="mb-2">
        <template #end>
          <div class="flex flex-wrap align-items-center gap-2">
            <FloatLabel>
              <label for="filterState">流水状态</label>
              <Select
                id="filterState"
                v-model="filterState"
                :options="bankStatementStateOptions"
                optionLabel="label"
                optionValue="value"
                placeholder="选择流水状态"
                showClear
                style="min-width: 12rem"
              />
            </FloatLabel>
            <FloatLabel>
              <label for="filterStatementNo">流水号</label>
              <InputText
                id="filterStatementNo"
                v-model="filterStatementNo"
              />
            </FloatLabel>
            <FloatLabel class="mr-2">
              <label for="filterPaymentName">付款方名称</label>
              <InputText
                id="filterPaymentName"
                v-model="filterPaymentName"
              />
            </FloatLabel>
          </div>
          <Button
            label="搜索"
            icon="pi pi-search"
            @click="handleSearch"
            class="mr-2 p-button-sm"
          />
          <Button
            label="重置"
            icon="pi pi-refresh"
            @click="resetFilters"
            outlined
            class="p-button-sm p-button-outlined"
          />
        </template>
      </Toolbar>

      <!-- 数据表格 -->
      <DataTable
        :value="bankStatements"
        :lazy="true"
        :paginator="true"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        :loading="loading"
        @page="onPage($event)"
        class="p-datatable-sm"
        showGridlines
        stripedRows
        scrollable
        scrollHeight="calc(100vh - 26rem)"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无银行流水数据</p>
          </div>
        </template>
        <Column field="statement_no" header="流水号" style="min-width: 15rem" />
        <Column field="amount" header="金额" style="min-width: 12rem">
          <template #body="slotProps">
            <span>{{
              formatCurrency(
                slotProps.data.amount,
                slotProps.data.currency_type
              )
            }}</span>
          </template>
        </Column>
        <Column
          field="statement_date"
          header="流水日期"
          style="min-width: 10rem"
        />
        <Column
          field="payment_name"
          header="付款方名称"
          style="min-width: 20rem"
        />
        <Column
          field="payment_bank_name"
          header="付款银行"
          style="min-width: 20rem"
        />
        <Column
          field="receive_bank_name"
          header="收款银行"
          style="min-width: 20rem"
        />
        <Column field="created_at" header="创建时间" style="min-width: 15rem">
          <template #body="slotProps">
            <span>{{ formatDateTime(slotProps.data.created_at) }}</span>
          </template>
        </Column>
        <Column header="操作" :exportable="false" style="min-width: 8rem">
          <template #body="slotProps">
            <Button
              icon="pi pi-pencil"
              rounded
              outlined
              @click="editBankStatement(slotProps.data)"
              v-tooltip.top="'编辑'"
              :disabled="!hasOperationPermission"
            />
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- 编辑抽屉 -->
    <Drawer
      v-model:visible="bankStatementDrawerVisible"
      position="right"
      :style="{ width: '60rem' }"
      :modal="true"
      :closable="true"
      :dismissable="true"
      :showCloseIcon="true"
      :header="`编辑银行流水 - ${editingBankStatement?.statement_no || ''}`"
      class="bank-statement-drawer p-fluid"
      @hide="closeDrawer"
    >
      <div v-if="editingBankStatement" class="p-4">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">基本信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-2 gap-4">
                <div class="field">
                  <label for="amount" class="required">金额</label>
                  <InputNumber
                    id="amount"
                    v-model="bankStatementForm.amount"
                    mode="decimal"
                    :minFractionDigits="2"
                    :maxFractionDigits="2"
                    :class="{
                      'p-invalid':
                        submitted &&
                        (!bankStatementForm.amount || fieldErrors.amount),
                    }"
                    class="w-full"
                  />
                  <small
                    class="p-error"
                    v-if="submitted && !bankStatementForm.amount"
                  >
                    金额不能为空
                  </small>
                  <small class="p-error" v-if="fieldErrors.amount">
                    {{ fieldErrors.amount }}
                  </small>
                </div>
                <div class="field">
                  <label for="currency_type" class="required">货币类型</label>
                  <Select
                    id="currency_type"
                    v-model="bankStatementForm.currency_type"
                    :options="currencyOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="请选择货币类型"
                    :class="{
                      'p-invalid':
                        submitted &&
                        (!bankStatementForm.currency_type ||
                          fieldErrors.currency_type),
                    }"
                    class="w-full"
                  />
                  <small
                    class="p-error"
                    v-if="submitted && !bankStatementForm.currency_type"
                  >
                    货币类型不能为空
                  </small>
                  <small class="p-error" v-if="fieldErrors.currency_type">
                    {{ fieldErrors.currency_type }}
                  </small>
                </div>
                <div class="field">
                  <label for="statement_no" class="required">流水号</label>
                  <InputText
                    id="statement_no"
                    v-model="bankStatementForm.statement_no"
                    :class="{
                      'p-invalid':
                        submitted &&
                        (!bankStatementForm.statement_no ||
                          fieldErrors.statement_no),
                    }"
                    class="w-full"
                  />
                  <small
                    class="p-error"
                    v-if="submitted && !bankStatementForm.statement_no"
                  >
                    流水号不能为空
                  </small>
                  <small class="p-error" v-if="fieldErrors.statement_no">
                    {{ fieldErrors.statement_no }}
                  </small>
                </div>
                <div class="field">
                  <label for="statement_date" class="required">流水日期</label>
                  <DatePicker
                    id="statement_date"
                    v-model="bankStatementForm.statement_date"
                    dateFormat="yy-mm-dd"
                    :class="{
                      'p-invalid':
                        submitted &&
                        (!bankStatementForm.statement_date ||
                          fieldErrors.statement_date),
                    }"
                    class="w-full"
                  />
                  <small
                    class="p-error"
                    v-if="submitted && !bankStatementForm.statement_date"
                  >
                    流水日期不能为空
                  </small>
                  <small class="p-error" v-if="fieldErrors.statement_date">
                    {{ fieldErrors.statement_date }}
                  </small>
                </div>
                <div class="field">
                  <label for="payment_name" class="required">付款方名称</label>
                  <InputText
                    id="payment_name"
                    v-model="bankStatementForm.payment_name"
                    :class="{
                      'p-invalid':
                        submitted &&
                        (!bankStatementForm.payment_name ||
                          fieldErrors.payment_name),
                    }"
                    class="w-full"
                  />
                  <small
                    class="p-error"
                    v-if="submitted && !bankStatementForm.payment_name"
                  >
                    付款方名称不能为空
                  </small>
                  <small class="p-error" v-if="fieldErrors.payment_name">
                    {{ fieldErrors.payment_name }}
                  </small>
                </div>
                <div class="field">
                  <label for="payment_bank_no">付款银行账号</label>
                  <InputText
                    id="payment_bank_no"
                    v-model="bankStatementForm.payment_bank_no"
                    :class="{ 'p-invalid': fieldErrors.payment_bank_no }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.payment_bank_no">
                    {{ fieldErrors.payment_bank_no }}
                  </small>
                </div>
                <div class="field">
                  <label for="payment_bank_name">付款银行名称</label>
                  <InputText
                    id="payment_bank_name"
                    v-model="bankStatementForm.payment_bank_name"
                    :class="{ 'p-invalid': fieldErrors.payment_bank_name }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.payment_bank_name">
                    {{ fieldErrors.payment_bank_name }}
                  </small>
                </div>
                <div class="field">
                  <label for="receive_bank_name">收款银行名称</label>
                  <InputText
                    id="receive_bank_name"
                    v-model="bankStatementForm.receive_bank_name"
                    :class="{ 'p-invalid': fieldErrors.receive_bank_name }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.receive_bank_name">
                    {{ fieldErrors.receive_bank_name }}
                  </small>
                </div>
                <div class="field">
                  <label for="receive_bank_no">收款银行账号</label>
                  <InputText
                    id="receive_bank_no"
                    v-model="bankStatementForm.receive_bank_no"
                    :class="{ 'p-invalid': fieldErrors.receive_bank_no }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.receive_bank_no">
                    {{ fieldErrors.receive_bank_no }}
                  </small>
                </div>
                <div class="field">
                  <label for="confirming_amount">待确认金额</label>
                  <InputNumber
                    id="confirming_amount"
                    v-model="bankStatementForm.confirming_amount"
                    mode="decimal"
                    :minFractionDigits="2"
                    :maxFractionDigits="2"
                    :class="{ 'p-invalid': fieldErrors.confirming_amount }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.confirming_amount">
                    {{ fieldErrors.confirming_amount }}
                  </small>
                </div>
                <div class="field">
                  <label for="confirmed_amount">已确认金额</label>
                  <InputNumber
                    id="confirmed_amount"
                    v-model="bankStatementForm.confirmed_amount"
                    mode="decimal"
                    :minFractionDigits="2"
                    :maxFractionDigits="2"
                    :class="{ 'p-invalid': fieldErrors.confirmed_amount }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.confirmed_amount">
                    {{ fieldErrors.confirmed_amount }}
                  </small>
                </div>
                <div class="field">
                  <label for="customer_num">客户编号</label>
                  <InputText
                    id="customer_num"
                    v-model="bankStatementForm.customer_num"
                    :class="{ 'p-invalid': fieldErrors.customer_num }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.customer_num">
                    {{ fieldErrors.customer_num }}
                  </small>
                </div>
                <div class="field">
                  <label for="customer_name">客户名称</label>
                  <InputText
                    id="customer_name"
                    v-model="bankStatementForm.customer_name"
                    :class="{ 'p-invalid': fieldErrors.customer_name }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.customer_name">
                    {{ fieldErrors.customer_name }}
                  </small>
                </div>
                <div class="field col-span-2">
                  <label for="description">备注</label>
                  <Textarea
                    id="description"
                    v-model="bankStatementForm.description"
                    rows="3"
                    :class="{ 'p-invalid': fieldErrors.description }"
                    class="w-full"
                  />
                  <small class="p-error" v-if="fieldErrors.description">
                    {{ fieldErrors.description }}
                  </small>
                </div>
              </div>
            </Fluid>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            @click="closeDrawer"
            class="p-button-outlined"
            :disabled="isSubmitting"
          />
          <Button
            label="保存"
            icon="pi pi-check"
            @click="saveBankStatement"
            :loading="isSubmitting"
          />
        </div>
      </template>
    </Drawer>
  </div>
</template>

<style scoped>
.bank-flow-container {
  padding: 1rem;
  background: #f8f9fa;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--p-surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--p-text-color-secondary);
  font-size: 1.1rem;
}

.form-section {
  margin-bottom: 1.5rem;
}

.section-header {
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-content {
  padding: 0.5rem 0;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--p-text-color);
}

.field label.required::after {
  content: " *";
  color: #ef4444;
}

.p-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.p-invalid {
  border-color: #ef4444 !important;
}

/* DataTable样式优化 */
:deep(.p-datatable) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

:deep(.p-datatable .p-datatable-header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e9ecef !important;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  border-bottom: 1px solid #f1f1f1 !important;
  font-size: 0.9rem !important;
  color: #1d1d1f !important;
  padding: 0.5rem 1rem !important;
}

:deep(.p-button-sm) {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

:deep(.p-drawer .p-drawer-header) {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

:deep(.p-drawer .p-drawer-content) {
  padding: 0;
}

:deep(.p-drawer .p-drawer-footer) {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 1rem;
}
</style>
